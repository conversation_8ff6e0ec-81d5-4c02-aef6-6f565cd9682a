<template>
	<view class="page-container">
		<Navbar :hideBtn="false" title="身份二维码" bgColor="#b7bbfa" :h5Show="false" :fixed="true" :titleStyle="{color:'#FFFFFF', fontSize:'16px', fontWeight:'500'}"></Navbar>
		<view class="qrcode-container">
			<canvas id="qrcode" canvas-id="qrcode" class="qrcode-canvas"></canvas>
		</view>
	</view>
</template>

<script>
	import UQRCode from 'uqrcodejs'
	import * as UserApi from "@/api/user.js"
	import aes from '@/utils/aes.js'
	import Navbar from '@/components/navbar/Navbar'
	export default {
		components: {
			Navbar
		},
		data() {
			return {

			}
		},
		onReady() {
			uni.showLoading()
			UserApi.patientCenterInfo().then(res=>{
				if(res.code==200){
					const {name,idNo,actualAddr,selfPhone} = res.data;
					// 获取uQRCode实例
					var qr = new UQRCode();
					// 设置二维码内容
					const a = (name?name:'')+"|"+(idNo?idNo:'')+"|"+(actualAddr?actualAddr:'')+"|"+(selfPhone?selfPhone:'');
					const info = aes.jiami(a)
					qr.data = info;
					// uni.$u.toast(aes.jiemi(info));
					// qr.data = "b6f7ca1cc794bfe72c964528e973b69d222a04c546260d47327793b71536757265679dcdbb31bd7289caeee76e233bd29f9ab6b3a5c74606961c49ad54a87aceac01f86f9010af01ede2429a36f6fc15f2e91b13d98177e9e45f23f5b1235e32d61e3998524ab66edc7c72eef79d96cb";
					// 设置二维码大小，必须与canvas设置的宽高一致
					qr.size = 300;
					// 调用制作二维码方法
					qr.make();
					// 获取canvas上下文
					var canvasContext = uni.createCanvasContext('qrcode', this); // 如果是组件，this必须传入
					// 设置uQRCode实例的canvas上下文
					qr.canvasContext = canvasContext;
					// 调用绘制方法将二维码图案绘制到canvas上
					qr.drawCanvas();
				}
				uni.hideLoading()
			})
			
		},
		methods: {

		}
	}
</script>

<style>
.page-container {
	height: 100vh;
	display: flex;
	flex-direction: column;
}

.qrcode-container {
	flex: 1;
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 20px;
}

.qrcode-canvas {
	width: 300px;
	height: 300px;
}
</style>